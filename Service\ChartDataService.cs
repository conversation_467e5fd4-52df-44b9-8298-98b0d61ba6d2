using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using ApexCharts;
using System.Drawing;
using Newtonsoft.Json;
using static System.Net.Mime.MediaTypeNames;
using Azure;

namespace Dashboard_Yuntech.Service
{
    public class ChartDataService
    {
        private readonly ExcelService _excelService;

        public ChartDataService(ExcelService excelService)
        {
            _excelService = excelService;
        }

        #region 通用圖表配置

        /// <summary>
        /// 創建標準的圖表顏色，提供多組美麗的色彩組合，每組都包含不同色系但有微妙變化
        /// </summary>
        /// <param name="count">需要的顏色數量</param>
        /// <param name="colorGroup">顏色組別 (0-4)，默認為0</param>
        /// <returns>顏色列表</returns>
        public List<string> GetStandardColors(int count = 1, int colorGroup = 0)
        {
            // 創建5組不同的顏色集合，每組都包含相同數量的基本色系，但有深淺/亮度差異
            var colorGroups = new List<List<string>>
            {
                // 第1組：插件標準色
                new List<string> {
                    "#008FFB", // 藍色
                    "#00E396", // 綠色
                    "#FEB019", // 黃色
                    "#FF4560", // 粉色
                    "#9C27B0"  // 紫色
                },

                // 第2組：高飽和度色彩
                new List<string> {
                    "#2979FF", // 亮藍色
                    "#FF1744", // 鮮紅色
                    "#00E676", // 亮綠色
                    "#FFEA00", // 亮黃色
                    "#AA00FF"  // 亮紫色
                },

                // 第3組：柔和色彩
                new List<string> {
                    "#64B5F6", // 淺藍色
                    "#EF9A9A", // 淺紅色
                    "#81C784", // 淺綠色
                    "#FFF59D", // 淺黃色
                    "#CE93D8"  // 淺紫色
                },

                // 第4組：深色調
                new List<string> {
                    "#1A237E", // 深藍色
                    "#B71C1C", // 深紅色
                    "#1B5E20", // 深綠色
                    "#F57F17", // 深黃色
                    "#4A148C"  // 深紫色
                },

                // 第5組：柔和漸變色
                new List<string> {
                    "#5C6BC0", // 靛藍色
                    "#EC407A", // 粉紅色
                    "#66BB6A", // 淺綠色
                    "#FDD835", // 黃色
                    "#8E24AA"  // 中紫色
                }
            };

            // 確保顏色組別在有效範圍內
            colorGroup = Math.Max(0, Math.Min(colorGroup, colorGroups.Count - 1));

            // 返回指定組別的顏色，最多返回指定數量
            return colorGroups[colorGroup].Take(count).ToList();
        }

        /// <summary>
        /// 依序跨組取得標準顏色，直到湊滿指定數量
        /// </summary>
        /// <param name="count">需要的顏色數量</param>
        /// <param name="startGroup">起始顏色組別 (預設0)</param>
        /// <returns>顏色列表</returns>
        public List<string> GetStandardColorsAcrossGroups(int count, int startGroup = 0)
        {
            var colorGroups = new List<List<string>>
            {
                new List<string> { "#008FFB", "#00E396", "#FEB019", "#FF4560", "#9C27B0" }, // 插件標準色
                new List<string> { "#2979FF", "#FF1744", "#00E676", "#FFEA00", "#AA00FF" }, // 高飽和度色彩
                new List<string> { "#64B5F6", "# EF9A9A", "#81C784", "#FFF59D", "#CE93D8" }, // 柔和色彩
                new List<string> { "#1A237E", "#B71C1C", "#1B5E20", "#F57F17", "#4A148C" }, // 深色調
                new List<string> { "#5C6BC0", "#EC407A", "#66BB6A", "#FDD835", "#8E24AA" } // 柔和漸變色
            };
            var result = new List<string>();
            int group = startGroup;
            while (result.Count < count) {
                var groupIdx = group % colorGroups.Count;
                foreach (var color in colorGroups[groupIdx]) {
                    if (result.Count >= count) break;
                    result.Add(color);
                }
                group++;
            }
            return result;
        }

        /// <summary>
        /// 取得格式化數字的 JavaScript 函式，將數字轉換為 K（千）和 M（百萬）單位，永遠只顯示整數
        /// </summary>
        /// <returns>JavaScript 格式化函式</returns>
        public string DataLabelFormat()
        {
            return @"function (value) {
                if (value === undefined || value === null || isNaN(value)) {
                    return '';
                }
                var absValue = Math.abs(value);
                var sign = value < 0 ? '-' : '';
                
                if (absValue >= 1000000) {
                    return sign + (absValue / 1000000).toFixed(1) + 'M';
                } else if (absValue >= 1000) {
                    return sign + (absValue / 1000).toFixed(1) + 'K';
                }
                return sign + absValue.toFixed(0);
            }";
        }



        /// <summary>
        /// 圖表軸線數值專用
        /// </summary>
        /// <returns></returns>
        public string ValueFormat()
        {
            return @"function (value) {
                 var absValue = Math.abs(value);
                 var sign = value < 0 ? '-' : '';
                 
                 if (absValue >= 1000000) {
                     return sign + Math.floor(absValue / 1000000) + 'M';
                 } else if (absValue >= 1000) {
                     return sign + Math.floor(absValue / 1000) + 'K';
                 }
                 return sign + Math.floor(absValue);
             }";
        }

        /// <summary>
        /// 取得專用於 RadialBar 的格式化函數，支援百分比轉換為實際值，並以 K（千）和 M（百萬）單位顯示整數
        /// </summary>
        /// <param name="maxValue">最大值，用於計算實際數值</param>
        /// <returns>JavaScript 格式化函式</returns>
        public string RadialBarFormat(decimal maxValue = 0)
        {
            return $@"function (val) {{
                // 獲取原始資料的最大值
                var maxAmount = {maxValue};

                // 計算原始數值 (反向轉換)
                var originalValue = (val * maxAmount / 100);
                var absValue = Math.abs(originalValue);
                var sign = originalValue < 0 ? '-' : '';

                // 格式化顯示（使用K和M單位）
                if (absValue >= 1000000) {{
                    return sign + Math.floor(absValue / 1000000) + 'M';
                }} else if (absValue >= 1000) {{
                    return sign + Math.floor(absValue / 1000) + 'K';
                }}
                return sign + Math.floor(absValue);
            }}";
        }

        /// <summary>
        /// 建立基本的長條圖選項
        /// </summary>
        public ApexChartOptions<T> CreateBarChartOptions<T>(
            List<string> colors,
            bool isHorizontal = false,
            bool showDataLabels = true,
            bool isStacked = false,
            StackType? stackedType = null) where T : class
        {
            // 取得數字格式化函式 - 整數格式
            string formatterFunction = DataLabelFormat();
            string formatterValue = ValueFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                //Stroke = new Stroke
                //{
                //    Show = true,
                //    Width = 3,
                //    Colors = _excelService.GetLighterColors(colors, 0.5)
                //},

                // 圖例設定
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Top,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // X 軸設定
                Xaxis = new XAxis
                {
                    Labels = new XAxisLabels
                    {
                        Style = new AxisLabelStyle { FontSize = "14px" }
                    }
                },

                // Y 軸設定
                Yaxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Labels = new YAxisLabels
                        {
                            Style = new AxisLabelStyle { FontSize = "14px" },
                            Formatter = formatterValue
                        }
                    }
                },

                // 工具提示設定
                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 圖表設定
                Chart = new Chart
                {
                    Type = ChartType.Bar,  // 修正：將類型設定為 Bar 而非 Line
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                    // 堆疊圖表設定
                    Stacked = isStacked,
                    StackType = stackedType
                },

                // 長條圖特定設定 - 為所有柱狀圖設定基本屬性
                PlotOptions = new PlotOptions
                {
                    Bar = new PlotOptionsBar
                    {
                        Horizontal = isHorizontal,  // 水平或垂直
                        BorderRadius = 5,      // 柱狀圖圓角
                        BorderRadiusApplication = BorderRadiusApplication.End
                    }
                },
            };

            // 數據標籤基本設定
            var dataLabels = new DataLabels
            {
                Enabled = showDataLabels,
                Style = new DataLabelsStyle
                {
                    FontSize = "12px",
                    Colors = new List<string> { "#fff" }
                },
                Formatter = formatterFunction
            };

            // 如果是水平長條圖，額外設定相關屬性
            if (isHorizontal)
            {
                // 針對橫式長條圖，需要為X軸添加格式化，因為數值在X軸上顯示
                options.Xaxis.Labels.Formatter = formatterValue;
                options.Yaxis[0].Labels.Formatter = null;
            }
            else
            {
                // 垂直長條圖設定 - 添加數據標籤設定
                if (options.PlotOptions?.Bar != null)
                {
                    options.PlotOptions.Bar.DataLabels = new PlotOptionsBarDataLabels
                    {
                        Total = new BarTotalDataLabels
                        {
                            Style = new BarDataLabelsStyle
                            {
                                FontWeight = "800",
                                Color = "#008FFB",
                            },
                            Formatter = formatterFunction
                        }
                    };
                }

                dataLabels.OffsetY = 3;
            }

            // 設定資料標籤
            options.DataLabels = dataLabels;

            return options;
        }

        /// <summary>
        /// 建立基本的面積圖選項
        /// </summary>
        public ApexChartOptions<T> CreateAreaChartOptions<T>(
            List<string> colors,
            bool showDataLabels = false,
            bool isSpline = true,
            bool isStacked = false,
            bool isMarkers = true) where T : class
        {
            // 取得數字格式化函式
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                // 圖例設定
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Top,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // X 軸設定
                Xaxis = new XAxis
                {
                    Labels = new XAxisLabels
                    {
                        Style = new AxisLabelStyle { FontSize = "14px" }
                    }
                },

                // Y 軸設定
                Yaxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Labels = new YAxisLabels
                        {
                            Style = new AxisLabelStyle { FontSize = "14px" },
                            // 格式化 Y 軸標籤，支援K/M等單位，只顯示整數
                            Formatter = ValueFormat()
                        }
                    }
                },

                // 工具提示設定
                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 圖表設定
                Chart = new Chart
                {
                    Type = ChartType.Area,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                    // 堆疊圖表設定
                    Stacked = isStacked
                },

                // 資料標籤
                DataLabels = new DataLabels
                {
                    Enabled = showDataLabels,
                    Style = new DataLabelsStyle
                    {
                        FontSize = "12px",
                    },

                    Formatter = formatterFunction
                },

            };

            if (!isMarkers)
            {
                options.Markers = new Markers { Size = 0 };
            }
            return options;
        }

        /// <summary>
        /// 建立基本的線型圖選項
        /// </summary>
        public ApexChartOptions<T> CreateLineChartOptions<T>(
            List<string> colors,
            bool showDataLabels = true,
            bool isSpline = true,
            bool isMarkers = true
            ) where T : class
        {
            // 取得數字格式化函式
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                // 線條設定
                Stroke = new Stroke { Curve = Curve.Smooth },

                // 圖例設定
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Top,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // X 軸設定
                Xaxis = new XAxis
                {
                    Labels = new XAxisLabels
                    {
                        Style = new AxisLabelStyle { FontSize = "14px", Colors = new List<string> { "#fff" } },
                        //Formatter = @"function(value, timestamp, index, opts) {
                        //    if (!value) return '';
                        //    var parts = value.split('/');
                        //    if (parts.length < 2) return '';

                        //    var year = parseInt(parts[0]);
                        //    var month = parseInt(parts[1]);

                        //    if (month === 1) {
                        //        return parts[0];
                        //    }
                        //    else if(year == 111 && month == 9){
                        //        return parts[0];
                        //    }
                        //    return '';
                        //}"
                    }
                },


                // Y 軸設定
                Yaxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Labels = new YAxisLabels
                        {
                            Style = new AxisLabelStyle { FontSize = "14px" },
                            // 格式化 Y 軸標籤，支援K/M等單位，只顯示整數
                            Formatter = ValueFormat()
                        }
                    }
                },

                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 圖表設定
                Chart = new Chart
                {
                    Type = ChartType.Line,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                },

                // 資料標籤
                DataLabels = new DataLabels
                {
                    Enabled = showDataLabels,
                    Style = new DataLabelsStyle
                    {
                        FontSize = "12px",
                    },
                    Formatter = formatterFunction
                },

                // 標記點設定
                Markers = new Markers
                {
                    Size = 5,
                    Hover = new MarkersHover
                    {
                        Size = 7
                    }
                },

                // 網格設定
                Grid = new Grid
                {
                    Row = new GridRow
                    {
                        Colors = new List<string> { "#333" },
                        Opacity = 0.1
                    },
                    Column = new GridColumn
                    {
                        Colors = new List<string> { "#333" },
                        Opacity = 0.1
                    }
                }
            };

            if (!isMarkers)
            {
                options.Markers = new Markers { Size = 0 };
            }
            return options;
        }

        public ApexChartOptions<T> CreatePieChartOptions<T>(
            List<string> colors,
            bool showDataLabels = true
        ) where T : class
        {
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                // 圖例設定
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Right,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // 資料標籤設定
                DataLabels = new DataLabels
                {
                    Enabled = showDataLabels,
                    Style = new DataLabelsStyle
                    {
                        FontSize = "14px"
                    },
                },

                // 工具提示設定
                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 移除白色邊框
                Stroke = new Stroke
                {
                    Show = false,
                    Width = 0
                },

                // 圖表設定
                Chart = new Chart
                {
                    Type = ChartType.Pie,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                    Height = 300,
                }
            };

            return options;
        }

        public ApexChartOptions<T> CreateDonutChartOptions<T>(
    List<string> colors,
    bool showDataLabels = true,
    bool isHalf = false
) where T : class
        {
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                // 圖例設定
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Right,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // 資料標籤設定
                DataLabels = new DataLabels
                {
                    Enabled = showDataLabels,
                    Style = new DataLabelsStyle
                    {
                        FontSize = "14px"
                    },
                },

                // 工具提示設定
                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 圖表設定
                Chart = new Chart
                {
                    Type = ChartType.Donut,    // 改成 Donut
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                    Height = 300,
                },

                // 移除白色邊框
                Stroke = new Stroke
                {
                    Show = false,
                    Width = 0
                },

                // Donut 相關選項
                PlotOptions = new PlotOptions
                {
                    Pie = new PlotOptionsPie
                    {
                        Donut = new PlotOptionsDonut
                        {
                            Labels = new DonutLabels
                            {
                                Show = true,
                                Name = new DonutLabelName { Show = true },
                                Value = new DonutLabelValue { Show = true, Formatter = formatterFunction },
                                Total = new DonutLabelTotal
                                {
                                    Show = true,
                                    Label = "總計",
                                    FontSize = "22px",
                                    Color = "#D807B8",
                                    // 修正Total為NaN的問題，正確計算總和
                                    Formatter = @"function(w) {
                                        let total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                                        if (total >= 1000000) {
                                            return Math.floor(total / 1000000) + 'M';
                                        } else if (total >= 1000) {
                                            return Math.floor(total / 1000) + 'K';
                                        }
                                        return Math.floor(total);
                                    }"
                                }
                            }
                        }

                    }
                }
            };

            if (isHalf)
            {
                // 想要什麼角度就什麼角度
                options.PlotOptions = new PlotOptions { Pie = new PlotOptionsPie { StartAngle = -90, EndAngle = 90 } };
            }
            return options;
        }


        public ApexChartOptions<T> CreateRadiaBarOptions<T>(
    List<string> colors,
    bool showDataLabels = true,
    decimal maxValue = 0,
    bool showLegend = true,
    bool showTotal = true,
    bool isHalf = false,
    bool isDash = false,
    bool isSingleItem = false
) where T : class
        {
            // 使用專用的 RadialBar 格式化函數
            string formatterFunction = RadialBarFormat(maxValue);

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                Legend = new Legend
                {
                    Show = showLegend,
                    Position = LegendPosition.Right,
                    FontSize = "16px",
                    Markers = new LegendMarkers
                    {
                        Width = 12,
                        Height = 12
                    }
                },

                Stroke = new Stroke
                {
                    Show = true,
                    LineCap = LineCap.Round,
                },

                Chart = new Chart
                {
                    Type = ChartType.RadialBar,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                },

                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                PlotOptions = new PlotOptions
                {
                    RadialBar = new PlotOptionsRadialBar
                    {
                        DataLabels = new RadialBarDataLabels
                        {
                            Name = new RadialBarDataLabelsName
                            {
                                Show = true,
                                FontSize = "14px",
                                OffsetY = -8
                            },
                            Value = new RadialBarDataLabelsValue
                            {
                                Show = true,
                                FontSize = "16px",
                                Formatter = formatterFunction
                            },
                            Total = new RadialBarDataLabelsTotal
                            {
                                Show = showTotal,
                                Label = "總計",
                                FontSize = "18px",
                                FontWeight = "bold",
                                // 計算總和的顯示
                                Formatter = $@"function (w) {{
                                    // 獲取原始資料的總和
                                    var total = 0;
                                    if (w.globals.series && w.globals.series.length > 0) {{
                                        const values = w.globals.series.map(function(s, i) {{
                                            // 計算原始值
                                            return (s * {maxValue} / 100);
                                        }});
                                        total = values.reduce(function(a, b) {{ return a + b; }}, 0);
                                    }}

                                    // 格式化顯示（使用K和M單位，只顯示整數）
                                    if (total >= 1000000) {{
                                        return Math.floor(total / 1000000) + 'M';
                                    }} else if (total >= 1000) {{
                                        return Math.floor(total / 1000) + 'K';
                                    }}
                                    return Math.floor(total);
                                }}"
                            }
                        },
                    }
                }
            };

            // 只有在單個項目時啟用漸層填充效果
            if (isSingleItem)
            {
                options.Fill = new Fill
                {
                    Type = new List<FillType> { FillType.Gradient },
                    Gradient = new FillGradient
                    {
                        Shade = GradientShade.Dark,     // 漸層的陰影為暗色調
                        Type = GradientType.Horizontal, // 漸層方向
                        ShadeIntensity = 0.5,           // 陰影強度
                        GradientToColors = colors,      // 漸層的目標顏色(按照漸層方向)
                        InverseColors = false,          // 不反轉顏色
                        OpacityFrom = 0.7,              // 設定漸層的透明度起始和結束值
                        OpacityTo = 1.0,
                        Stops = new List<double> { 0, 100.0 } // 設定漸層的開始點
                    }
                };
            }
            else
            {
                // 多個項目時使用普通填充
                options.Fill = new Fill
                {
                    Type = new List<FillType> { FillType.Solid }
                };
            }

            // 一半模式
            if (isHalf)
            {
                options.PlotOptions = new PlotOptions { Pie = new PlotOptionsPie { StartAngle = -90, EndAngle = 90 } };
            }

            // 線條模式
            if (isDash)
            {
                options.Stroke = new Stroke { DashArray = 4 };
                options.Fill = new Fill {};
            }

            return options;
        }

        // 新增方法：計算百分比
        public decimal CalculatePercentOfMax(decimal value, decimal maxValue)
        {
            return maxValue > 0 ? value * 100 / maxValue : 0;
        }

        public ApexChartOptions<T> CreateRadarChartOptions<T>(
    List<string> colors,
    bool showDataLabels = true
) where T : class
        {
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                // 圖表設定：類型為 Radar，背景色為深色，關閉工具列
                Chart = new Chart
                {
                    Type = ChartType.Radar,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c"
                },

                // 工具提示設定：使用自訂的格式化函數
                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 圖例設定：顯示於底部，設定字體大小及標記尺寸
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Bottom,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // 邊框設定：設定寬度與顏色
                Stroke = new Stroke
                {
                    Show = true,
                    Width = 1,
                    Colors = colors
                },

                // Y軸設定:值的文字大小
                Yaxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Labels = new YAxisLabels
                        {
                            Style = new AxisLabelStyle { FontSize = "14px" },
                            Formatter = ValueFormat()
                        }
                    }
                },

                // X軸設定:雷達端點的文字
                Xaxis = new XAxis
                {
                    Labels = new XAxisLabels
                    {
                        Style = new AxisLabelStyle {
                            FontSize = "16px",
                            Colors = colors
                        }
                    }
                },

                // 擴大雷達圖的尺寸
                PlotOptions = new PlotOptions
                {
                    Radar = new PlotOptionsRadar
                    {
                        Size = 200, // 擴大雷達圖尺寸比例
                        Polygons = new RadarPolygons
                        {
                            // StrokeColors = "red",        // 數值階級邊框線
                            // ConnectorColors = "green",   // 中間那條線(XY軸)
                            // Fill = new RadarPolygonsFill { Colors = new List<string> { "#282a2c" } } // 填滿的顏色
                        }
                    }
                }
            };

            return options;
        }

        /// <summary>
        /// 建立PolarArea圖表選項
        /// </summary>
        public ApexChartOptions<T> CreatePolarAreaChartOptions<T>(
            List<string> colors,
            bool showDataLabels = true
        ) where T : class
        {
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                Chart = new Chart
                {
                    Type = ChartType.PolarArea,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c"
                },

                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Bottom,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // 資料標籤設定
                DataLabels = new DataLabels
                {
                    Enabled = showDataLabels,
                    Style = new DataLabelsStyle
                    {
                        FontSize = "14px",
                    },
                },

                // 填充設定
                Fill = new Fill
                {
                    Opacity = 0.95
                },

                Yaxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Labels = new YAxisLabels
                        {
                            Style = new AxisLabelStyle
                            {
                                FontSize = "14px",
                            },
                            Formatter = ValueFormat()
                        }
                    }
                }

            };

            return options;
        }

        /// <summary>
        /// 建立Treemap圖表選項
        /// </summary>
        public ApexChartOptions<T> CreateTreemapChartOptions<T>(
            List<string> colors,
            bool showDataLabels = true
        ) where T : class
        {
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                Colors = colors,

                Chart = new Chart
                {
                    Type = ChartType.Treemap,
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c"
                },

                Stroke = new Stroke
                {
                    Show = true,
                    Width = 3,
                    Colors = new List<string> { "#282a2c" }
                },

                // 工具提示設定
                Tooltip = new Tooltip
                {
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // Treemap特定選項
                PlotOptions = new PlotOptions
                {
                    Treemap = new PlotOptionsTreemap
                    {
                        // 每個區塊使用不同顏色
                        Distributed = true,

                        // 色塊之間的間距
                        EnableShades = true,
                        // 色塊顏色深淺變化
                        ShadeIntensity = 0.2,

                        // 使用圓角
                        BorderRadius = 5
                    }
                }

            };

            return options;
        }

        /// <summary>
        /// 建立混合圖表選項，可在同一圖表中展示不同類型的視覺化
        /// </summary>
        public ApexChartOptions<T> CreateMixedChartOptions<T>(
            List<string> colors,
            bool showDataLabels = true,
            bool isMarkers = true
        ) where T : class
        {
            string formatterFunction = DataLabelFormat();

            var options = new ApexChartOptions<T>
            {
                //Colors = colors,

                Chart = new Chart
                {
                    Type = ChartType.Line, // 必須設定一個基本類型，然後在系列中覆蓋
                    Toolbar = new Toolbar { Show = false },
                    Background = "#282a2c",
                    Stacked = false  // 確保不是堆疊圖表
                },

                Stroke = new Stroke
                {
                    Curve = Curve.Smooth, // 平滑曲線
                    Width = 3,  // 線條粗細
                },

                // 工具提示設定
                Tooltip = new Tooltip
                {
                    Shared = true, // 所有系列共享同一工具提示
                    Intersect = false, // 不需要精確相交，只要接近數據點就顯示
                    Y = new TooltipY
                    {
                        Formatter = formatterFunction
                    }
                },

                // 圖例設定
                Legend = new Legend
                {
                    Show = true,
                    Position = LegendPosition.Bottom,
                    FontSize = "16px",
                    Markers = new LegendMarkers { Width = 12, Height = 12 }
                },

                // X 軸設定
                // X 軸設定
                Xaxis = new XAxis
                {
                    Labels = new XAxisLabels
                    {
                        Style = new AxisLabelStyle { FontSize = "14px", Colors = new List<string> { "#fff" } },
                        //Formatter = @"function(value, timestamp, index, opts) {
                        //    if (!value) return '';
                        //    var parts = value.split('/');
                        //    if (parts.length < 2) return '';

                        //    var year = parseInt(parts[0]);
                        //    var month = parseInt(parts[1]);

                        //    if (month === 1) {
                        //        return parts[0];
                        //    }
                        //    else if(year == 111 && month == 9){
                        //        return parts[0];
                        //    }
                        //    return '';
                        //}"
                    }
                },

                // Y 軸設定 - 只設定主Y軸，第二Y軸會在使用時添加
                Yaxis = new List<YAxis>
                {
                    new YAxis
                    {
                        Labels = new YAxisLabels
                        {
                            Style = new AxisLabelStyle { FontSize = "14px" },
                            Formatter = ValueFormat()
                        }
                    }
                },

                // 數據標籤配置
                DataLabels = new DataLabels
                {
                    Enabled = showDataLabels,
                    Style = new DataLabelsStyle
                    {
                        FontSize = "12px",
                    },
                    Formatter = formatterFunction
                },

                // 長條圖特定設定
                PlotOptions = new PlotOptions
                {
                    Bar = new PlotOptionsBar
                    {
                        ColumnWidth = "50%",  // 柱狀圖寬度
                        BorderRadius = 20,     // 柱狀圖圓角
                        BorderRadiusApplication = BorderRadiusApplication.End
                    }
                },

                // 圖表填充
                Fill = new Fill
                {
                    Type = new List<FillType> { FillType.Solid }
                },
            };

            if (!isMarkers) {
                options.Markers = new Markers{ Size = 0 };
            }

            return options;
        }

        #endregion

        #region 數據處理方法

        /// <summary>
        /// 通用的資料篩選與格式化方法，用於準備給 AzureChart 的 TableData
        /// </summary>
        /// <typeparam name="TContainer">包含資料集合的容器類型</typeparam>
        /// <typeparam name="TItem">實際的資料項目類型</typeparam>
        /// <param name="sourceData">完整的資料來源</param>
        /// <param name="dataSelector">從容器中提取資料集合的函式</param>
        /// <param name="propertySelector">從資料項目中提取用於篩選的屬性值的函式</param>
        /// <param name="filterKeyword">篩選關鍵字</param>
        /// <returns>格式化後的表格資料</returns>
        public List<Dictionary<string, object>>? CreateFilteredTableData<TContainer, TItem>(
            IEnumerable<TContainer>? sourceData,
            Func<TContainer, IEnumerable<TItem>> dataSelector,
            Func<TItem, string> propertySelector,
            string filterKeyword) where TItem : class
        {
            if (sourceData == null || !sourceData.Any())
            {
                return null;
            }

            // 篩選資料
            var filteredItems = sourceData
                .SelectMany(dataSelector)
                .Where(item =>
                {
                    var propValue = propertySelector(item);
                    return !string.IsNullOrEmpty(propValue) && propValue.Contains(filterKeyword);
                })
                .ToList();

            // 根據年份屬性排序 (如果存在)
            var itemType = typeof(TItem);
            var yearProperty = itemType.GetProperty("AcademicYear") ?? itemType.GetProperty("Year");
            if (yearProperty != null)
            {
                filteredItems = filteredItems.OrderByDescending(item => yearProperty.GetValue(item)).ToList();
            }

            if (!filteredItems.Any())
            {
                return new List<Dictionary<string, object>>();
            }

            // 僅選擇包含 [JsonProperty] 特性的屬性
            var propertiesToInclude = typeof(TItem).GetProperties()
                .Where(p => p.GetCustomAttribute<JsonPropertyAttribute>() != null)
                .ToList();

            // 將篩選後的物件列表轉換為 TableData 格式
            return filteredItems.Select(item =>
            {
                var row = new Dictionary<string, object>();
                foreach (var prop in propertiesToInclude)
                {
                    // 再次獲取特性以取得 PropertyName 作為標頭
                    var jsonProperty = prop.GetCustomAttribute<JsonPropertyAttribute>();
                    if (jsonProperty != null)
                    {
                        var header = jsonProperty.PropertyName;
                        var value = prop.GetValue(item);
                        row[header] = value ?? "";
                    }
                }
                return row;
            }).ToList();
        }

        /// <summary>
        /// 通用模型轉換方法，將原始表格數據轉換為指定的模型類型
        /// </summary>
        /// <typeparam name="T">目標模型類型，必須有 SchoolName 屬性和 ValueColumns 字典</typeparam>
        /// <param name="tableData">原始表格數據</param>
        /// <param name="schoolNameColumn">學校名稱欄位</param>
        /// <param name="valueColumns">數值欄位字典，Key為顯示名稱，Value為Excel欄位名稱</param>
        /// <param name="displayNamesMapping">顯示名稱映射，用於設置特定屬性</param>
        /// <param name="markTopItem">排名第一賞星星</param>
        /// <param name="keyPropertyName">模型中主鍵屬性名稱，預設為"SchoolName"</param>
        /// <returns>目標模型列表</returns>
        public List<T> ConvertToModel<T>(
            List<Dictionary<string, object>> tableData,
            string schoolNameColumn,
            Dictionary<string, string> valueColumns,
            Dictionary<ChartColumnType, string> displayNamesMapping = null,
            bool markTopItem = true,
            string keyPropertyName = "SchoolName") where T : class, new()
        {
            // 確保至少有主鍵欄位
            if (string.IsNullOrEmpty(schoolNameColumn) || valueColumns == null || !valueColumns.Any())
            {
                return new List<T>();
            }

            // 建立顯示名稱到欄位類型的反向映射（用於後續查找）
            Dictionary<string, ChartColumnType> displayNameToType = new Dictionary<string, ChartColumnType>();
            if (displayNamesMapping != null)
            {
                foreach (var mapping in displayNamesMapping)
                {
                    displayNameToType[mapping.Value] = mapping.Key;
                }
            }

            // 獲取模型類型的屬性信息
            var modelType = typeof(T);
            var keyProperty = modelType.GetProperty(keyPropertyName);
            var valueColumnsProperty = modelType.GetProperty("ValueColumns");

            if (keyProperty == null || valueColumnsProperty == null)
            {
                throw new ArgumentException($"類型 {modelType.Name} 必須具有 {keyPropertyName} 和 ValueColumns 屬性");
            }

            var result = tableData
                .Where(row => row.ContainsKey(schoolNameColumn))
                .GroupBy(row => row[schoolNameColumn]?.ToString())
                .Select(group =>
                {
                    // 創建新模型實例
                    var model = new T();

                    // 設置主鍵值
                    keyProperty.SetValue(model, group.Key);

                    // 創建並設置 ValueColumns 字典
                    var valueColumnsDictionary = new Dictionary<string, decimal>();
                    valueColumnsProperty.SetValue(model, valueColumnsDictionary);

                    // 處理每個數值欄位
                    foreach (var column in valueColumns)
                    {
                        string displayName = column.Key;
                        string columnName = column.Value;

                        // 計算該欄位的總和
                        decimal total = group.Sum(row =>
                        {
                            if (row.ContainsKey(columnName) && row[columnName] != null)
                            {
                                var valueStr = row[columnName]?.ToString();
                                return decimal.TryParse(valueStr, out var val) ? val : 0;
                            }
                            return 0;
                        });

                        // 存儲該欄位的值到對應字典
                        valueColumnsDictionary[displayName] = total;

                        // 根據欄位類型設置特定屬性
                        if (displayNameToType.TryGetValue(displayName, out ChartColumnType columnType))
                        {
                            // 查找相應的屬性
                            string propertyName = GetPropertyNameForColumnType(columnType);
                            var property = modelType.GetProperty(propertyName);

                            if (property != null && property.PropertyType == typeof(decimal))
                            {
                                property.SetValue(model, total);
                            }
                        }
                    }

                    return model;
                })
                .Where(m => m != null && keyProperty.GetValue(m) != null)
                .ToList();

            return result;
        }

        /// <summary>
        /// 將原始表格數據轉換為研究經費數據模型
        /// </summary>
        /// <param name="tableData">原始表格數據</param>
        /// <param name="schoolNameColumn">學校名稱欄位</param>
        /// <param name="valueColumns">數值欄位字典，Key為顯示名稱，Value為Excel欄位名稱</param>
        /// <param name="displayNamesMapping">顯示名稱映射，用於設置特定屬性</param>
        /// <param name="markTopItem">排名第一賞星星</param>
        /// <param name="keyPropertyName">模型中主鍵屬性名稱，預設為"SchoolName"</param>
        /// <returns>面積圖數據模型列表</returns>
        public List<ResearchMoney_Combine> ConvertToAreaChaet(
            List<Dictionary<string, object>> tableData,
            string schoolNameColumn,
            Dictionary<string, string> valueColumns,
            Dictionary<ChartColumnType, string> displayNamesMapping = null,
            bool markTopItem = true,
            string keyPropertyName = "SchoolName")
        {
            return ConvertToModel<ResearchMoney_Combine>(tableData, schoolNameColumn, valueColumns, displayNamesMapping, markTopItem, keyPropertyName);
        }

        /// <summary>
        /// 將原始表格數據轉換為多值比較模型
        /// </summary>
        /// <param name="tableData">原始表格數據</param>
        /// <param name="schoolNameColumn">學校名稱欄位</param>
        /// <param name="valueColumns">數值欄位字典，Key為顯示名稱，Value為Excel欄位名稱</param>
        /// <param name="displayNamesMapping">顯示名稱映射，用於設置特定屬性</param>
        /// <param name="markTopItem">排名第一賞星星</param>
        /// <param name="keyPropertyName">模型中主鍵屬性名稱，預設為"SchoolName"</param>
        /// <returns>多值比較模型列表</returns>
        public List<MultiValueModel> ConvertToMultiValue(
            List<Dictionary<string, object>> tableData,
            string schoolNameColumn,
            Dictionary<string, string> valueColumns,
            Dictionary<ChartColumnType, string> displayNamesMapping = null,
            bool markTopItem = true,
            string keyPropertyName = "SchoolName")
        {
            return ConvertToModel<MultiValueModel>(tableData, schoolNameColumn, valueColumns, displayNamesMapping, markTopItem, keyPropertyName);
        }


        /// <summary>
        /// 根據欄位類型獲取對應的屬性名稱
        /// ★ 僅需填入 【Excel】 轉成統計圖表的資料!
        /// </summary>
        private string GetPropertyNameForColumnType(ChartColumnType columnType)
        {
            switch (columnType)
            {
                case ChartColumnType.SmallMoney:
                    return "SmallMoney";
                case ChartColumnType.GovMoney:
                    return "GovMoney";
                case ChartColumnType.TeacherMoney:
                    return "TeacherMoney";
                case ChartColumnType.TeacherCount:
                    return "TeacherCount";
                case ChartColumnType.StudentCount:
                    return "StudentCount";
                case ChartColumnType.Value:
                    return "Value";

                // TCS經費相關欄位
                case ChartColumnType.TIM_BusinessCost:
                    return "TIM_BusinessCost";
                case ChartColumnType.TIM_EquipmentCost:
                    return "TIM_EquipmentCost";
                case ChartColumnType.TIM_SoftwareCost:
                    return "TIM_SoftwareCost";

                // GPU使用率相關欄位
                case ChartColumnType.TIM_GPURateP:
                    return "TIM_GPURateP";

                // GPU使用人數相關欄位
                case ChartColumnType.TIM_GPU_UserCnt:
                    return "TIM_GPU_UserCnt";
                case ChartColumnType.TIM_GPU_UserCnt2:
                    return "TIM_GPU_UserCnt2";

                // 系統登入次數相關欄位
                case ChartColumnType.TIS_LoginCnt:
                    return "TIS_LoginCnt";

                // 系統開發數相關欄位
                case ChartColumnType.TIS_SysCnt:
                    return "TIS_SysCnt";

                // 系統組三大需求表欄位
                case ChartColumnType.TCS_FormCnt:
                    return "TCS_FormCnt";

                // PC教室相關欄位
                case ChartColumnType.TIM_HardwareCost:
                    return "TIM_HardwareCost";
                case ChartColumnType.TIM_SoftwarePurchase:
                    return "TIM_SoftwarePurchase";
                case ChartColumnType.TIM_SystemDevCost:
                    return "TIM_SystemDevCost";
                case ChartColumnType.TIM_MaintenanceCost:
                    return "TIM_MaintenanceCost";
                case ChartColumnType.TIM_RentalCost:
                    return "TIM_RentalCost";
                case ChartColumnType.TIM_CloudCost:
                    return "TIM_CloudCost";
                case ChartColumnType.TIM_LicenseFee:
                    return "TIM_LicenseFee";
                case ChartColumnType.TIM_DataCommCost:
                    return "TIM_DataCommCost";
                case ChartColumnType.TIM_SuppliesCost:
                    return "TIM_SuppliesCost";
                case ChartColumnType.TIM_PCRoomTotal:
                    return "TIM_PCRoomTotal";

                // 外國學位生相關欄位
                case ChartColumnType.Foreign_Student_Bachelor:
                    return "BachelorCount";
                case ChartColumnType.Foreign_Student_Associate:
                    return "AssociateCount";
                case ChartColumnType.Foreign_Student_Master:
                    return "MasterCount";
                case ChartColumnType.Foreign_Student_Master_Part:
                    return "MasterPartTimeCount";
                case ChartColumnType.Foreign_Student_Doctor:
                    return "DoctorCount";

                default:
                    return columnType.ToString();
            }
        }
        #endregion
    }

    #region 數據模型

    /// <summary>
    /// 圖表欄位類型枚舉(調整欄位在這邊)
    /// </summary>
    public enum ChartColumnType
    {
        Category,    // 類別欄位
        Value,       // 數值欄位
        SmallMoney,  // 小計
        GovMoney,    // 政府部門資助
        CompanyMoney, // 企業部門資助
        OtherMoney,   // 其它資助
        TeacherMoney, // 教師承接經費
        NonProfitMoney, // 非營利機構資助
        StudentCount,  // 學生人數
        TeacherCount, // 專任教師數
        StuTeaRatio, // 生師比

        // 圖書館相關欄位
        Lib_Ch_AllType, // 中文紙本圖書總數
        Lib_Ch_Philosophy, // 中文紙本圖書-哲學類
        Lib_Ch_Religion,   // 中文紙本圖書-宗教類
        Lib_Ch_Science,    // 中文紙本圖書-自然科學類
        Lib_Ch_Applied,    // 中文紙本圖書-應用科學類
        Lib_Ch_Social,     // 中文紙本圖書-社會科學類
        Lib_Ch_History,    // 中文紙本圖書-史地類
        Lib_Ch_Language,   // 中文紙本圖書-語言文學類
        Lib_Ch_Art,        // 中文紙本圖書-藝術類
        Lib_En_Total,      // 外文紙本書總數
        Lib_BoundJournals, // 期刊合訂本
        Lib_E_DB,          // 電子資料-資料庫(種)
        Lib_E_CD,          // 電子資料-光碟資料庫(種)
        Lib_E_Journals,    // 電子期刊種數
        Lib_E_Book,        // 電子書冊數
        Lib_AV,            // 視聽資料(件)
        Lib_Paper,         // 現期書報-報紙(種)
        Lib_Paper_Ch,      // 現期書報期刊-中日文(種)
        Lib_Paper_En,      // 現期書報期刊-西文(種)
        Lib_Seat,          // 圖書閱覽座位數
        Lib_User,          // 借閱人次（紙本 + 非書）
        Lib_Lend,          // 借閱冊次（紙本 + 非書）
        Lib_OnlineUse,     // 線上資料庫檢索人次（含電子書與電子期刊）
        Lib_Money,         // 圖書資源經費
        Lib_StudCount,      // 註冊學生人數(含大學部、碩博士班)
        Lib_StudPerMoney,   // 學生平均享有圖資經費
        Lib_CertifiedStaff, // 經政府機關考試合格人員
        Lib_TechnicalStaff, // 技術職務人員
        Lib_FacultyAdmin,   // 教師兼行政工作
        Lib_SeniorStaff,    // 服務滿3年以上館員
        Lib_TotalStaff,     // 總人數

        // ★這邊是Excel欄位
        // 系統組
        TIS_LoginCnt,         // 系統登入次數
        TIS_SysCnt,        // 系統開發數
        TCS_FormCnt,      // 系統組三大需求表

        // 應用組
        TIM_GPURateP,       // GPU使用率（百分比）
        TIM_GPU_UserCnt,    // GPU使用人數
        TIM_GPU_UserCnt2,   // GPU使用人數(分頁2)
        // PC教室相關欄位
        TIM_HardwareCost,       // 硬體設備費
        TIM_SoftwarePurchase,   // 軟體購置費
        TIM_SystemDevCost,      // 系統開發費
        TIM_MaintenanceCost,    // 資訊操作維護費
        TIM_RentalCost,         // 資訊設備租金
        TIM_CloudCost,          // 雲端服務費
        TIM_LicenseFee,         // 軟體使用費
        TIM_DataCommCost,       // 數據通訊費
        TIM_SuppliesCost,       // 電腦用品及耗材
        TIM_PCRoomTotal,        // 合計

        // 網路組


        // Excel 結束

        // 外國學位生相關欄位
        Foreign_Student_Bachelor,    // 四技人數
        Foreign_Student_Associate,   // 二技人數
        Foreign_Student_Master,      // 碩士班人數
        Foreign_Student_Master_Part, // 碩士在職專班人數
        Foreign_Student_Doctor,      // 博士班人數

        // 財務類相關欄位 (Final_Models.cs)
        Final_AvailableFunds,        // 可用資金
        Final_CashFlow_Operations,   // 業務活動現金流
        Final_CashFlow_Investing,    // 投資活動現金流
        Final_CashFlow_Financing,    // 籌資活動現金流
        Final_CashFlow_ExchangeRate, // 匯率變動影響
        Final_CashFlow_Total,        // 現金流總計
        Final_TuitionFees,           // 學雜費收入
        Final_TotalRevenue,          // 業務總收入
        Final_TuitionRatio,          // 學雜費收入比率

        // 研究類相關欄位 (Res_Models.cs)
        Res_FullTimeTeachers,        // 專任教師數
        Res_TotalFunding,            // 產學合作總經費
        Res_AvgFunding,              // 每師平均產學合作經費
        Res_TotalSchoolFunding,      // 全校總經費
        Res_TotalProjectFunding,     // 承接計畫總經費
        Res_GovProjectFunding,       // 政府計畫資助
        Res_EnterpriseProjectFunding, // 企業計畫資助
        Res_OtherProjectFunding,     // 其他計畫資助
        Res_FundingRatio,            // 計畫經費比率
        Res_AvgProjectFunding,       // 每師平均計畫經費

        // 學生類相關欄位 (Stud_Models.cs)
        Stud_DeptCode,               // 系所代碼
        Stud_DeptName,               // 系所名稱
        Stud_ClassSystem,            // 學制班別
        Stud_Total,                  // 在學學生總數
        Stud_Male,                   // 男學生數
        Stud_Female,                 // 女學生數
        Stud_ForeignTotal,           // 外國學生總數
        Stud_ForeignMale,            // 外國男學生數
        Stud_ForeignFemale,          // 外國女學生數
        Stud_ForeignRatio,           // 外國學生比率

        // 教職類相關欄位 (Teach_Models.cs)
        Teach_DayStudents,           // 日間學制學生數
        Teach_FullTimeTeachers,      // 日間專任教師數
        Teach_STRatio,               // 日間生師比
        Teach_UnitCode,              // 單位代碼
        Teach_UnitName,              // 單位名稱
        Teach_ForeignTotal,          // 外籍教師總數
        Teach_ForeignMaleTotal,      // 外籍男教師總數
        Teach_ForeignFemaleTotal,    // 外籍女教師總數
        Teach_ForeignProfMale,       // 外籍男教授
        Teach_ForeignProfFemale,     // 外籍女教授
        Teach_ForeignAssocProfMale,  // 外籍男副教授
        Teach_ForeignAssocProfFemale, // 外籍女副教授
        Teach_ForeignAsstProfMale,   // 外籍男助理教授
        Teach_ForeignAsstProfFemale, // 外籍女助理教授
        Teach_ForeignLecturerMale,   // 外籍男講師
        Teach_ForeignLecturerFemale, // 外籍女講師
        Teach_ForeignOtherMale,      // 外籍其他男教師
        Teach_ForeignOtherFemale,    // 外籍其他女教師
        Teach_ProjectStaffTotal,     // 專案教學人員總數
        Teach_ProjectStaffProf,      // 專案教學教授
        Teach_ProjectStaffAssocProf, // 專案教學副教授
        Teach_ProjectStaffAsstProf,  // 專案教學助理教授
        Teach_ProjectStaffLecturer,  // 專案教學講師
        Teach_ProjectStaffRatio,     // 專案教學人員比率
        Teach_TechStaffTotal,        // 專業技術人員總數
        Teach_TechStaffProf,         // 專業技術教授
        Teach_TechStaffAssocProf,    // 專業技術副教授
        Teach_TechStaffAsstProf,     // 專業技術助理教授
        Teach_TechStaffLecturer,     // 專業技術講師
        Teach_TechStaffRatio,        // 專業技術人員比率
        Teach_TechTeacherTotal,      // 專業技術教師總數
        Teach_TechTeacherProf,       // 專業技術教師教授
        Teach_TechTeacherAssocProf,  // 專業技術教師副教授
        Teach_TechTeacherAsstProf,   // 專業技術教師助理教授
        Teach_TechTeacherLecturer,   // 專業技術教師講師
        Teach_TechTeacherRatio,      // 專業技術教師比率

        // 校務類-住宿相關欄位 (UA_1stDoomRate)
        UA_Applicants,               // 申請宿舍人數
        UA_OwnDormResidents,         // 自有宿舍住宿人數
        UA_OwnDormMale,              // 自有宿舍男生
        UA_OwnDormFemale,            // 自有宿舍女生
        UA_RentedDormResidents,      // 租賃宿舍住宿人數
        UA_RentedDormMale,           // 租賃宿舍男生
        UA_RentedDormFemale,         // 租賃宿舍女生
        UA_OffCampusRenters,         // 校外租屋人數
        UA_OffCampusMale,            // 校外租屋男生
        UA_OffCampusFemale,          // 校外租屋女生
        UA_AccommodationRatio,       // 住宿比率

        // 校務類-全外語授課相關欄位 (UA_ForeignCollege)
        UA_UnitCode,                 // 單位代碼
        UA_UnitName,                 // 單位名稱
        UA_ClassSystem,              // 學制班別
        UA_Language,                 // 授課語言
        UA_LocalStudentsTotal,       // 本國學生總數
        UA_LocalStudentsMale,        // 本國男學生
        UA_LocalStudentsFemale,      // 本國女學生
        UA_ForeignStudentsTotal,     // 境外學生總數
        UA_ForeignStudentsMale,      // 境外男學生
        UA_ForeignStudentsFemale,    // 境外女學生


        // 未知
        // TCS經費相關欄位
        TIM_BusinessCost,    // 業務費
        TIM_EquipmentCost,   // 設備費
        TIM_SoftwareCost,    // 軟體費
    }

    /// <summary>
    /// 圖表設定類別(以後可以拿掉)
    /// </summary>
    public class ChartColumnSettings
    {
        /// <summary>
        /// 研究類-非師均圖
        /// </summary>
        public static ChartColumnSettings Research_Combine => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.SmallMoney, "學校承接「產學合作」計畫經費-小計" },
                { ChartColumnType.GovMoney, "學校承接「產學合作」計畫經費-政府部門資助" },
                { ChartColumnType.CompanyMoney, "學校承接「產學合作」計畫經費-企業部門資助" },
                { ChartColumnType.OtherMoney, "學校承接「產學合作」計畫經費-其他單位資助" },
                { ChartColumnType.TeacherMoney, "專任教師每師平均承接「產學合作」計畫經費" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.SmallMoney, "總計" },
                { ChartColumnType.GovMoney, "政府資助" },
                { ChartColumnType.CompanyMoney, "企業資助" },
                { ChartColumnType.OtherMoney, "其他單位資助" },
                { ChartColumnType.TeacherMoney, "教師承接經費" }
            }
        };

        /// <summary>
        /// 教職類-日間生師比
        /// </summary>
        public static ChartColumnSettings DayStuTeaRatio => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TeacherCount, "日間專任教師(含助教)" },
                { ChartColumnType.StudentCount, "日間學制學生數" },
                { ChartColumnType.StuTeaRatio, "日間生師比" },
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TeacherCount, "專任教師數" },
                { ChartColumnType.StudentCount, "在學學生數" },
                { ChartColumnType.StuTeaRatio, "日間生師比" },
            }
        };

        /// <summary>
        /// 校務類-圖書館資源
        /// </summary>
        public static ChartColumnSettings Library => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.Lib_Ch_AllType, "紙本圖書收藏冊數(冊)-中文紙本圖書-總類" },
                { ChartColumnType.Lib_Ch_Philosophy, "紙本圖書收藏冊數(冊)-中文紙本圖書-哲學類" },
                { ChartColumnType.Lib_Ch_Religion, "紙本圖書收藏冊數(冊)-中文紙本圖書-宗教類" },
                { ChartColumnType.Lib_Ch_Science, "紙本圖書收藏冊數(冊)-中文紙本圖書-自然科學類" },
                { ChartColumnType.Lib_Ch_Applied, "紙本圖書收藏冊數(冊)-中文紙本圖書-應用科學類" },
                { ChartColumnType.Lib_Ch_Social, "紙本圖書收藏冊數(冊)-中文紙本圖書-社會科學類" },
                { ChartColumnType.Lib_Ch_History, "紙本圖書收藏冊數(冊)-中文紙本圖書-史地類(含世界史地類)" },
                { ChartColumnType.Lib_Ch_Language, "紙本圖書收藏冊數(冊)-中文紙本圖書-語言文學類" },
                { ChartColumnType.Lib_Ch_Art, "紙本圖書收藏冊數(冊)-中文紙本圖書-藝術類" },
                { ChartColumnType.Lib_En_Total, "紙本圖書收藏冊數(冊)-外文紙本圖書" },
                { ChartColumnType.Lib_BoundJournals, "期刊合訂本(未以紙本圖書編目)(冊)" },
                { ChartColumnType.Lib_E_DB, "電子資料可使用量-線上資料庫(種)" },
                { ChartColumnType.Lib_E_CD, "電子資料可使用量-光碟及其他類型資料庫(種)" },
                { ChartColumnType.Lib_E_Journals, "電子資料可使用量-電子期刊(種)" },
                { ChartColumnType.Lib_E_Book, "電子資料可使用量-電子書(冊)" },
                { ChartColumnType.Lib_AV, "視聽資料(件)" },
                { ChartColumnType.Lib_Paper, "現期書報-報紙(種)" },
                { ChartColumnType.Lib_Paper_Ch, "現期書報(種)期刊-中日文(種)" },
                { ChartColumnType.Lib_Paper_En, "現期書報(種)期刊-西文(種)" },
                { ChartColumnType.Lib_Seat, "圖書館服務(人次)-圖書閱覽座位數" },
                { ChartColumnType.Lib_User, "圖書館服務(人次)-借閱人次(僅含紙本圖書及非書資料)" },
                { ChartColumnType.Lib_Lend, "圖書館服務(人次)-借閱冊次(僅含紙本圖書及非書資料)" },
                { ChartColumnType.Lib_OnlineUse, "圖書館服務(人次)-線上及光碟資料庫(含電子書及電子期刊)檢索人次" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.Lib_Ch_AllType, "中文藏書量" },
                { ChartColumnType.Lib_Ch_Philosophy, "中文紙本圖書-哲學類" },
                { ChartColumnType.Lib_Ch_Religion, "中文紙本圖書-宗教類" },
                { ChartColumnType.Lib_Ch_Science, "中文紙本圖書-自然科學類" },
                { ChartColumnType.Lib_Ch_Applied, "中文紙本圖書-應用科學類" },
                { ChartColumnType.Lib_Ch_Social, "中文紙本圖書-社會科學類" },
                { ChartColumnType.Lib_Ch_History, "中文紙本圖書-史地類" },
                { ChartColumnType.Lib_Ch_Language, "中文紙本圖書-語言文學類" },
                { ChartColumnType.Lib_Ch_Art, "中文紙本圖書-藝術類" },
                { ChartColumnType.Lib_En_Total, "外文藏書量" },
                { ChartColumnType.Lib_BoundJournals, "期刊合訂本" },
                { ChartColumnType.Lib_E_DB, "電子資料庫" },
                { ChartColumnType.Lib_E_CD, "電子光碟資料庫" },
                { ChartColumnType.Lib_E_Journals, "電子期刊數" },
                { ChartColumnType.Lib_E_Book, "電子書數" },
                { ChartColumnType.Lib_AV, "視聽資料" },
                { ChartColumnType.Lib_Paper, "報紙數" },
                { ChartColumnType.Lib_Paper_Ch, "中文報紙期刊" },
                { ChartColumnType.Lib_Paper_En, "西文報紙期刊" },
                { ChartColumnType.Lib_Seat, "閱覽座位數" },
                { ChartColumnType.Lib_User, "借閱人次" },
                { ChartColumnType.Lib_Lend, "借閱冊次" },
                { ChartColumnType.Lib_OnlineUse, "線上資料庫檢索人次" }
            }
        };

        /// <summary>
        /// 資訊中心經費
        /// </summary>
        public static ChartColumnSettings TCSMoney => new ChartColumnSettings
        {
            CategoryColumn = "年度",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIM_BusinessCost, "業務費(NT$)" },
                { ChartColumnType.TIM_EquipmentCost, "設備費(NT$)" },
                { ChartColumnType.TIM_SoftwareCost, "軟體費(NT$)" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIM_BusinessCost, "業務費" },
                { ChartColumnType.TIM_EquipmentCost, "設備費" },
                { ChartColumnType.TIM_SoftwareCost, "軟體費" }
            }
        };

        /// <summary>
        /// GPU使用率
        /// </summary>
        public static ChartColumnSettings GPURate => new ChartColumnSettings
        {
            CategoryColumn = "時間",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIM_GPURateP, "GPU使用率" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIM_GPURateP, "GPU使用率" }
            }
        };

        /// <summary>
        /// GPU使用人數
        /// </summary>
        public static ChartColumnSettings GPUUser => new ChartColumnSettings
        {
            CategoryColumn = "時間",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIM_GPU_UserCnt, "GPU使用人數統計" },
                { ChartColumnType.TIM_GPU_UserCnt2, "人次統計" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIM_GPU_UserCnt, "GPU使用人數統計" },
                { ChartColumnType.TIM_GPU_UserCnt2, "使用人次統計" }
            }
        };

        /// <summary>
        /// 系統登入次數
        /// </summary>
        public static ChartColumnSettings LoginCounts => new ChartColumnSettings
        {
            CategoryColumn = "REQ_APPID", // 系統名稱
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIS_LoginCnt, "cnt" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIS_LoginCnt, "登入次數" }
            }
        };

        /// <summary>
        /// 系統開發數
        /// </summary>
        public static ChartColumnSettings SystemCounts => new ChartColumnSettings
        {
            CategoryColumn = "時間",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIS_SysCnt, "自行開發系統數" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TIS_SysCnt, "系統開發數" }
            }
        };


        // 添加系統組三大需求表的ChartColumnSettings
        public static ChartColumnSettings TCSForm => new ChartColumnSettings
        {
            CategoryColumn = "表單類型",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TCS_FormCnt, "筆數" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TCS_FormCnt, "筆數" }
            }
        };

        /// <summary>
        /// 外國學位生
        /// </summary>
        public static ChartColumnSettings ForeignStudent => new ChartColumnSettings
        {
            CategoryColumn = "年度",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.Foreign_Student_Bachelor, "四技" },
                { ChartColumnType.Foreign_Student_Associate, "二技" },
                { ChartColumnType.Foreign_Student_Master, "碩士班" },
                { ChartColumnType.Foreign_Student_Master_Part, "碩士在職專班" },
                { ChartColumnType.Foreign_Student_Doctor, "博士班" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.Foreign_Student_Bachelor, "四技" },
                { ChartColumnType.Foreign_Student_Associate, "二技" },
                { ChartColumnType.Foreign_Student_Master, "碩士班" },
                { ChartColumnType.Foreign_Student_Master_Part, "碩士在職專班" },
                { ChartColumnType.Foreign_Student_Doctor, "博士班" }
            }
        };

        //★以下都刪掉

        /// <summary>
        /// 預設的多值比較圖表設定
        /// </summary>
        public static ChartColumnSettings DefaultMultiValueSettings => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TeacherCount, "學校承接「產學合作」計畫經費-小計" },
                { ChartColumnType.StudentCount, "學校承接「產學合作」計畫經費-政府部門資助" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TeacherCount, "小計" },
                { ChartColumnType.StudentCount, "政府資助" }
            }
        };

        /// <summary>
        /// 預設的教師與學生數圖表設定
        /// </summary>
        public static ChartColumnSettings DefaultTeacherStudentSettings => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TeacherCount, "學校承接「產學合作」計畫經費-小計" },
                { ChartColumnType.StudentCount, "學校承接「產學合作」計畫經費-政府部門資助" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.TeacherCount, "小計" },
                { ChartColumnType.StudentCount, "政府資助" }
            }
        };

        /// <summary>
        /// 預設的研究經費圖表設定
        /// </summary>
        public static ChartColumnSettings DefaultResearchMoneySettings => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.SmallMoney, "學校承接「產學合作」計畫經費-小計" },
                { ChartColumnType.GovMoney, "學校承接「產學合作」計畫經費-政府部門資助" },
                { ChartColumnType.CompanyMoney, "學校承接「產學合作」計畫經費-企業部門資助" },
                { ChartColumnType.OtherMoney, "學校承接「產學合作」計畫經費-其他單位資助" },
                { ChartColumnType.TeacherMoney, "專任教師每師平均承接「產學合作」計畫經費" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.SmallMoney, "小計" },
                { ChartColumnType.GovMoney, "政府資助" },
                { ChartColumnType.CompanyMoney, "企業資助" },
                { ChartColumnType.OtherMoney, "其他單位資助" },
                { ChartColumnType.TeacherMoney, "教師承接經費" }
            }
        };

        /// <summary>
        /// 預設的教師數圖表設定
        /// </summary>
        public static ChartColumnSettings DefaultTeacherCountSettings => new ChartColumnSettings
        {
            CategoryColumn = "學校名稱",
            ValueColumns = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.Value, "專任教師數" }
            },
            DisplayNames = new Dictionary<ChartColumnType, string>
            {
                { ChartColumnType.Value, "專任教師數" }
            }
        };

        /// <summary>
        /// 創建通用圖表設定
        /// </summary>
        /// <param name="categoryColumn">類別欄位名稱</param>
        /// <param name="valueColumn">數值欄位名稱</param>
        /// <param name="valueName">數值欄位顯示名稱</param>
        /// <returns>圖表設定</returns>
        public static ChartColumnSettings CreateGenericSettings(string categoryColumn, string valueColumn, string valueName = null)
        {
            return new ChartColumnSettings
            {
                CategoryColumn = categoryColumn,
                ValueColumns = new Dictionary<ChartColumnType, string>
                {
                    { ChartColumnType.Value, valueColumn }
                },
                DisplayNames = new Dictionary<ChartColumnType, string>
                {
                    { ChartColumnType.Value, valueName ?? valueColumn }
                }
            };
        }

        /// <summary>
        /// 分類欄位（X軸）
        /// </summary>
        public string CategoryColumn { get; set; }

        /// <summary>
        /// 數值欄位映射（欄位類型 -> Excel欄位名稱）
        /// </summary>
        public Dictionary<ChartColumnType, string> ValueColumns { get; set; } = new Dictionary<ChartColumnType, string>();

        /// <summary>
        /// 顯示名稱映射（欄位類型 -> 顯示名稱）
        /// </summary>
        public Dictionary<ChartColumnType, string> DisplayNames { get; set; } = new Dictionary<ChartColumnType, string>();

        /// <summary>
        /// 獲取用於圖表的欄位名稱字典（顯示名稱 -> Excel欄位名稱）
        /// </summary>
        public Dictionary<string, string> GetChartValueColumns()
        {
            var result = new Dictionary<string, string>();

            foreach (var column in ValueColumns)
            {
                string displayName = DisplayNames.ContainsKey(column.Key)
                    ? DisplayNames[column.Key]
                    : column.Key.ToString();

                result[displayName] = column.Value;
            }

            return result;
        }

        /// <summary>
        /// Clone，增加指定欄位類型（其餘保留）
        /// </summary>
        public static ChartColumnSettings AddColumnChart(ChartColumnSettings baseSettings, params ChartColumnType[] columnsToKeep)
        {
            // 複製新設定
            var newSettings = new ChartColumnSettings
            {
                CategoryColumn = baseSettings.CategoryColumn,
                ValueColumns = new Dictionary<ChartColumnType, string>(),
                DisplayNames = new Dictionary<ChartColumnType, string>()
            };

            foreach (var type in columnsToKeep)
            {
                if (baseSettings.ValueColumns.TryGetValue(type, out var columnName))
                    newSettings.ValueColumns[type] = columnName;

                if (baseSettings.DisplayNames.TryGetValue(type, out var displayName))
                    newSettings.DisplayNames[type] = displayName;
            }

            return newSettings;
        }

        /// <summary>
        /// Clone，移除指定欄位類型（其餘保留）
        /// </summary>
        public static ChartColumnSettings DeleteColumnChart(ChartColumnSettings baseSettings, params ChartColumnType[] columnsToRemove)
        {
            // 複製新設定
            var newSettings = new ChartColumnSettings
            {
                CategoryColumn = baseSettings.CategoryColumn,
                ValueColumns = new Dictionary<ChartColumnType, string>(baseSettings.ValueColumns),
                DisplayNames = new Dictionary<ChartColumnType, string>(baseSettings.DisplayNames)
            };

            foreach (var type in columnsToRemove)
            {
                newSettings.ValueColumns.Remove(type);
                newSettings.DisplayNames.Remove(type);
            }

            return newSettings;
        }
    }

    /// <summary>
    /// 研究類-研究經費數據模型
    /// </summary>
    public class ResearchMoney_Combine
    {
        public string SchoolName { get; set; }    // 學校名稱
        public decimal SmallMoney { get; set; } // 小計
        public decimal CompanyMoney { get; set; } // 企業資助
        public decimal GovMoney { get; set; } // 政府部門資助
        public decimal OtherMoney { get; set; } // 其他單位資助
        public decimal TeacherMoney { get; set; } // 教師承接

        public decimal PercentSmallMoney { get; set; }
        public Dictionary<string, decimal> ValueColumns { get; set; } // 其他數值欄位
    }

    /// <summary>
    /// 教職類-日間生師比
    /// </summary>
    public class DayStuTeaRatioModel
    {
        public string SchoolName { get; set; }     // 學校名稱
        public decimal TeacherCount  { get; set; }  // 教師人數
        public decimal StudentCount { get; set; }     // 學生人數
        public decimal StuTeaRatio { get; set; }     // 生師比

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>(); // 其他數值欄位
    }

    /// <summary>
    /// 校務類-圖書館
    /// </summary>
    public class LibModel
    {
        public string SchoolName { get; set; }     // 學校名稱

        public decimal Lib_Ch_Total { get; set; }    // 中文紙本圖書總數
        public decimal Lib_Ch_Philosophy { get; set; }      // 中文紙本圖書-哲學類
        public decimal Lib_Ch_Religion { get; set; }        // 中文紙本圖書-宗教類
        public decimal Lib_Ch_Science { get; set; }         // 中文紙本圖書-自然科學類
        public decimal Lib_Ch_Applied { get; set; }         // 中文紙本圖書-應用科學類
        public decimal Lib_Ch_Social { get; set; }          // 中文紙本圖書-社會科學類
        public decimal Lib_Ch_History { get; set; }         // 中文紙本圖書-史地類
        public decimal Lib_Ch_Language { get; set; }        // 中文紙本圖書-語言文學類
        public decimal Lib_Ch_Art { get; set; }             // 中文紙本圖書-藝術類

        public decimal Lib_En_Total { get; set; }            // 外文紙本書總數

        public decimal Lib_BoundJournals { get; set; }      // 期刊合訂本

        public decimal Lib_E_DB { get; set; }               // 電子資料-資料庫(種)
        public decimal Lib_E_CD { get; set; }               // 電子資料-光碟資料庫(種)
        public decimal Lib_E_Journals { get; set; }         // 電子期刊種數
        public decimal Lib_E_Book { get; set; }             // 電子書冊數

        public decimal Lib_AV { get; set; }                 // 視聽資料(件)

        public decimal Lib_Paper { get; set; }              // 現期書報-報紙(種)
        public decimal Lib_Paper_Ch { get; set; }           // 現期書報期刊-中日文(種)
        public decimal Lib_Paper_En { get; set; }           // 現期書報期刊-西文(種)

        public decimal Lib_Seat { get; set; }               // 圖書閱覽座位數
        public decimal Lib_Usr { get; set; }                // 借閱人次（紙本 + 非書）
        public decimal Lib_Lend { get; set; }               // 借閱冊次（紙本 + 非書）
        public decimal Lib_OnlineUse { get; set; }          // 線上資料庫檢索人次（含電子書與電子期刊）

        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>(); // 其他數值欄位
    }

    // 202505 系統登入次數
    public class LoginModel
    {
        public string REQ_APPID { get; set; }  // 系統名
        public decimal Cnt { get; set; }          // 登入次數
        public Dictionary<string, decimal> ValueColumns { get; set; }
    }


    // GPU 使用人數
    public class GPUserModel
    {
        public string Time { get; set; }     // 時間
        public decimal GPUUserCount { get; set; }  // GPU 使用人數統計
        public decimal AccumulatedUserCount { get; set; }  // GPU 使用人數累計

        public decimal GPUUserCount2 { get; set; }  // 人次統計 (第二分頁)
        public Dictionary<string, decimal> ValueColumns { get; set; }
    }

    // GPU 使用率
    public class GPURateModel
    {
        public string Time { get; set; }     // 時間
        public decimal GPURate { get; set; }   // GPU 使用率（百分比）

        public Dictionary<string, decimal> ValueColumns { get; set; }
    }


    // TCS 經費
    public class TCSMoneyModel
    {
        public string Time { get; set; }              // 年度
        public decimal BusinessCost{ get; set; } // 業務費 (NT$)
        public decimal EquipmentCost { get; set; } // 設備費 (NT$)
        public decimal SoftwareCost { get; set; }  // 軟體費 (NT$)


        public Dictionary<string, decimal> ValueColumns { get; set; }
    }


    // 系統開發數
    public class SysCountModel
    {
        public string Time { get; set; }     // 時間
        public decimal SystemCount { get; set; }   // 自行開發系統數

        public Dictionary<string, decimal> ValueColumns { get; set; }
    }


    // 系統組需求表
    public class TCSForm
    {
        public string FormType { get; set; }   // 表單類型
        public decimal Count { get; set; }     // 筆數

        public Dictionary<string, decimal> ValueColumns { get; set; }
    }

    // 個別學校RadiaBar數據模型
    public class SchoolRadiaBarData
    {
        public string SchoolName { get; set; }
        public decimal SmallMoney { get; set; }
        public decimal PercentSmallMoney { get; set; }
        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();
        public ApexChartOptions<SchoolRadiaBarData> ChartOptions { get; set; }
    }


    /// <summary>
    /// 多值比較數據模型（用於堆疊長條圖）
    /// </summary>
    public class MultiValueModel
    {
        public string SchoolName { get; set; }    // 學校名稱
        public decimal TeacherCount { get; set; } // 專任教師數
        public decimal StudentCount { get; set; } // 學生人數
        public Dictionary<string, decimal> ValueColumns { get; set; } // 其他數值欄位
    }

    #endregion
}