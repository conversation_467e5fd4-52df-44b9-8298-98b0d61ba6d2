@page "/Internet"
@using System.Diagnostics
@using Dashboard_Yuntech.Service
@using Dashboard_Yuntech.Models.ChartModels
@using ApexCharts
@rendermode InteractiveServer
@implements IDisposable
@inject WebScrapingService WebScrapingService
@inject ChartDataService ChartDataService
@inject IJSRuntime JSRuntime

<PageTitle>網路流量爬蟲</PageTitle>

<MyAlert Text="網路流量"></MyAlert>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">YUNTECH 網路流量監控</h5>
                    <div class="text-muted">
                        下次更新倒數: @countdownSeconds 秒
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-10">
                            <label for="urlInput" class="form-label">Cacti 圖表 URL:</label>
                            <input type="text" class="form-control" id="urlInput" @bind="cactiUrl"
                                   placeholder="http://cnms.yuntech.edu.tw/cacti/graph.php?action=view&local_graph_id=1711&rra_id=all" />
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button class="btn btn-primary w-100" @onclick="ScrapeAllTimeRanges" disabled="@isLoading">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>爬取中...</span>
                                    }
                                    else
                                    {
                                        <span>重新爬取</span>
                                    }
                                </button>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <strong>錯誤:</strong> @errorMessage
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(successMessage))
                    {
                        <div class="alert alert-success" role="alert">
                            <strong>成功:</strong> @successMessage
                        </div>
                    }

                    @if (isLoading)
                    {
                        <div class="text-center mt-4">
                            <div class="spinner-border" role="status"></div>
                            <p class="mt-2">正在爬取所有時間範圍的資料，請稍候...</p>
                            @if (!string.IsNullOrEmpty(currentTimeRange))
                            {
                                <p class="text-muted">目前正在處理: @timeRangeDisplayNames[currentTimeRange]</p>
                            }
                        </div>
                    }

                    @if (allTimeRangeData.Any())
                    {
                        <div class="mt-4">
                            <h5>網路流量資料總覽</h5>
                            
                            <!-- Hourly 每小時 -->
                            @if (allTimeRangeData.ContainsKey("Hourly"))
                            {
                                var data = allTimeRangeData["Hourly"];
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-area me-2"></i>
                                            每20分鐘 (1分鐘間隔)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!string.IsNullOrEmpty(data.ErrorMessage))
                                        {
                                            <div class="alert alert-warning">
                                                <strong>資料載入失敗:</strong> @data.ErrorMessage
                                            </div>
                                        }
                                        else if (data.CsvData != null && data.CsvData.Any())
                                        {
                                            <div class="mb-3">
                                                <button class="btn btn-success btn-sm" @onclick="@(() => DownloadCsvFile("Hourly"))">
                                                    <i class="fas fa-download"></i> 下載 CSV
                                                </button>
                                            </div>
                                            @if (data.ChartData != null && data.ChartData.Any())
                                            {
                                                <div class="mb-4">
                                                    <NetworkChart Title="每20分鐘 (1分鐘間隔) - 網路流量統計圖表"
                                                        ChartData="@data.ChartData"
                                                        ChartOptions="@data.ChartOptions"
                                                        ModalId="networkTrafficModal_Hourly" />
                                                </div>
                                            }
                                            <div class="mt-3">
                                                <h6>CSV 資料 (共 @data.CsvData.Count 筆) @(!string.IsNullOrEmpty(data.DataDate) ? $"- 資料日期: {data.DataDate}" : ""):</h6>
                                                <div class="table-responsive" style="max-height: 400px;">
                                                    <table class="table table-striped table-hover table-sm">
                                                        <thead class="table-dark sticky-top">
                                                            <tr>
                                                                @if (data.CsvData.Any() && data.CsvData.First().Keys != null)
                                                                {
                                                                    @foreach (var header in data.CsvData.First().Keys)
                                                                    {
                                                                        <th>@header</th>
                                                                    }
                                                                }
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach (var row in data.CsvData)
                                                            {
                                                                <tr>
                                                                    @foreach (var value in row.Values)
                                                                    {
                                                                        <td>@value</td>
                                                                    }
                                                                </tr>
                                                            }
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                <strong>提示:</strong> 沒有可顯示的資料
                                            </div>
                                        }
                                    </div>
                                </div>
                            }

                            <!-- Daily 每日 -->
                            @if (allTimeRangeData.ContainsKey("Daily"))
                            {
                                var data = allTimeRangeData["Daily"];
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-area me-2"></i>
                                            每2小時 (5分鐘間隔)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!string.IsNullOrEmpty(data.ErrorMessage))
                                        {
                                            <div class="alert alert-warning">
                                                <strong>資料載入失敗:</strong> @data.ErrorMessage
                                            </div>
                                        }
                                        else if (data.CsvData != null && data.CsvData.Any())
                                        {
                                            <div class="mb-3">
                                                <button class="btn btn-success btn-sm" @onclick="@(() => DownloadCsvFile("Daily"))">
                                                    <i class="fas fa-download"></i> 下載 CSV
                                                </button>
                                            </div>
                                            @if (data.ChartData != null && data.ChartData.Any())
                                            {
                                                <div class="mb-4">
                                                    <NetworkChart Title="每2小時 (5分鐘間隔) - 網路流量統計圖表"
                                                        ChartData="@data.ChartData"
                                                        ChartOptions="@data.ChartOptions"
                                                        ModalId="networkTrafficModal_Daily" />
                                                </div>
                                            }
                                            <div class="mt-3">
                                                <h6>CSV 資料 (共 @data.CsvData.Count 筆) @(!string.IsNullOrEmpty(data.DataDate) ? $"- 資料日期: {data.DataDate}" : ""):</h6>
                                                <div class="table-responsive" style="max-height: 400px;">
                                                    <table class="table table-striped table-hover table-sm">
                                                        <thead class="table-dark sticky-top">
                                                            <tr>
                                                                @if (data.CsvData.Any() && data.CsvData.First().Keys != null)
                                                                {
                                                                    @foreach (var header in data.CsvData.First().Keys)
                                                                    {
                                                                        <th>@header</th>
                                                                    }
                                                                }
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach (var row in data.CsvData)
                                                            {
                                                                <tr>
                                                                    @foreach (var value in row.Values)
                                                                    {
                                                                        <td>@value</td>
                                                                    }
                                                                </tr>
                                                            }
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                <strong>提示:</strong> 沒有可顯示的資料
                                            </div>
                                        }
                                    </div>
                                </div>
                            }

                            <!-- Weekly 每週 -->
                            @if (allTimeRangeData.ContainsKey("Weekly"))
                            {
                                var data = allTimeRangeData["Weekly"];
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-area me-2"></i>
                                            每日 (30分鐘間隔)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!string.IsNullOrEmpty(data.ErrorMessage))
                                        {
                                            <div class="alert alert-warning">
                                                <strong>資料載入失敗:</strong> @data.ErrorMessage
                                            </div>
                                        }
                                        else if (data.CsvData != null && data.CsvData.Any())
                                        {
                                            <div class="mb-3">
                                                <button class="btn btn-success btn-sm" @onclick="@(() => DownloadCsvFile("Weekly"))">
                                                    <i class="fas fa-download"></i> 下載 CSV
                                                </button>
                                            </div>
                                            @if (data.ChartData != null && data.ChartData.Any())
                                            {
                                                <div class="mb-4">
                                                    <NetworkChart Title=" 每日 (30分鐘間隔) - 網路流量統計圖表"
                                                        ChartData="@data.ChartData"
                                                        ChartOptions="@data.ChartOptions"
                                                        ModalId="networkTrafficModal_Weekly" />
                                                </div>
                                            }
                                            <div class="mt-3">
                                                <h6>CSV 資料 (共 @data.CsvData.Count 筆) @(!string.IsNullOrEmpty(data.DataDate) ? $"- 資料日期: {data.DataDate}" : ""):</h6>
                                                <div class="table-responsive" style="max-height: 400px;">
                                                    <table class="table table-striped table-hover table-sm">
                                                        <thead class="table-dark sticky-top">
                                                            <tr>
                                                                @if (data.CsvData.Any() && data.CsvData.First().Keys != null)
                                                                {
                                                                    @foreach (var header in data.CsvData.First().Keys)
                                                                    {
                                                                        <th>@header</th>
                                                                    }
                                                                }
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach (var row in data.CsvData)
                                                            {
                                                                <tr>
                                                                    @foreach (var value in row.Values)
                                                                    {
                                                                        <td>@value</td>
                                                                    }
                                                                </tr>
                                                            }
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                <strong>提示:</strong> 沒有可顯示的資料
                                            </div>
                                        }
                                    </div>
                                </div>
                            }

                            <!-- Monthly 每月 -->
                            @if (allTimeRangeData.ContainsKey("Monthly"))
                            {
                                var data = allTimeRangeData["Monthly"];
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-area me-2"></i>
                                            每周 (2小時間隔)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!string.IsNullOrEmpty(data.ErrorMessage))
                                        {
                                            <div class="alert alert-warning">
                                                <strong>資料載入失敗:</strong> @data.ErrorMessage
                                            </div>
                                        }
                                        else if (data.CsvData != null && data.CsvData.Any())
                                        {
                                            <div class="mb-3">
                                                <button class="btn btn-success btn-sm" @onclick="@(() => DownloadCsvFile("Monthly"))">
                                                    <i class="fas fa-download"></i> 下載 CSV
                                                </button>
                                            </div>
                                            @if (data.ChartData != null && data.ChartData.Any())
                                            {
                                                <div class="mb-4">
                                                    <NetworkChart Title="每周 (2小時間隔) - 網路流量統計圖表"
                                                        ChartData="@data.ChartData"
                                                        ChartOptions="@data.ChartOptions"
                                                        ModalId="networkTrafficModal_Monthly" />
                                                </div>
                                            }
                                            <div class="mt-3">
                                                <h6>CSV 資料 (共 @data.CsvData.Count 筆) @(!string.IsNullOrEmpty(data.DataDate) ? $"- 資料日期: {data.DataDate}" : ""):</h6>
                                                <div class="table-responsive" style="max-height: 400px;">
                                                    <table class="table table-striped table-hover table-sm">
                                                        <thead class="table-dark sticky-top">
                                                            <tr>
                                                                @if (data.CsvData.Any() && data.CsvData.First().Keys != null)
                                                                {
                                                                    @foreach (var header in data.CsvData.First().Keys)
                                                                    {
                                                                        <th>@header</th>
                                                                    }
                                                                }
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach (var row in data.CsvData)
                                                            {
                                                                <tr>
                                                                    @foreach (var value in row.Values)
                                                                    {
                                                                        <td>@value</td>
                                                                    }
                                                                </tr>
                                                            }
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                <strong>提示:</strong> 沒有可顯示的資料
                                            </div>
                                        }
                                    </div>
                                </div>
                            }

                            <!-- Yearly 每年 -->
                            @if (allTimeRangeData.ContainsKey("Yearly"))
                            {
                                var data = allTimeRangeData["Yearly"];
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-area me-2"></i>
                                            每月 (1天間隔)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if (!string.IsNullOrEmpty(data.ErrorMessage))
                                        {
                                            <div class="alert alert-warning">
                                                <strong>資料載入失敗:</strong> @data.ErrorMessage
                                            </div>
                                        }
                                        else if (data.CsvData != null && data.CsvData.Any())
                                        {
                                            <div class="mb-3">
                                                <button class="btn btn-success btn-sm" @onclick="@(() => DownloadCsvFile("Yearly"))">
                                                    <i class="fas fa-download"></i> 下載 CSV
                                                </button>
                                            </div>
                                            @if (data.ChartData != null && data.ChartData.Any())
                                            {
                                                <div class="mb-4">
                                                    <NetworkChart Title="每月 (1天間隔) - 網路流量統計圖表"
                                                        ChartData="@data.ChartData"
                                                        ChartOptions="@data.ChartOptions"
                                                        ModalId="networkTrafficModal_Yearly" />
                                                </div>
                                            }
                                            <div class="mt-3">
                                                <h6>CSV 資料 (共 @data.CsvData.Count 筆) @(!string.IsNullOrEmpty(data.DataDate) ? $"- 資料日期: {data.DataDate}" : ""):</h6>
                                                <div class="table-responsive" style="max-height: 400px;">
                                                    <table class="table table-striped table-hover table-sm">
                                                        <thead class="table-dark sticky-top">
                                                            <tr>
                                                                @if (data.CsvData.Any() && data.CsvData.First().Keys != null)
                                                                {
                                                                    @foreach (var header in data.CsvData.First().Keys)
                                                                    {
                                                                        <th>@header</th>
                                                                    }
                                                                }
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach (var row in data.CsvData)
                                                            {
                                                                <tr>
                                                                    @foreach (var value in row.Values)
                                                                    {
                                                                        <td>@value</td>
                                                                    }
                                                                </tr>
                                                            }
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                <strong>提示:</strong> 沒有可顯示的資料
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string cactiUrl = "http://cnms.yuntech.edu.tw/cacti/graph.php?action=view&local_graph_id=1711&rra_id=all";
    private bool isLoading = false;
    private string errorMessage = "";
    private string successMessage = "";
    private string currentTimeRange = "";
    private int scrapeIntervalMinutes = 1; // 在這裡設定爬蟲間隔（分鐘）
    private int countdownSeconds;
    
    // 所有時間範圍的資料
    private List<string> timeRanges = new() { "Hourly", "Daily", "Weekly", "Monthly", "Yearly" };
    private Dictionary<string, string> timeRangeDisplayNames = new()
    {
        { "Hourly", "每小時 (1分鐘間隔)" },
        { "Daily", "每日 (5分鐘間隔)" },
        { "Weekly", "每週 (30分鐘間隔)" },
        { "Monthly", "每月 (2小時間隔)" },
        { "Yearly", "每年 (1天間隔)" }
    };
    
    // 儲存各時間範圍的資料
    private Dictionary<string, TimeRangeData> allTimeRangeData = new();
    
    public class TimeRangeData
    {
        public string CsvContent { get; set; } = "";
        public List<Dictionary<string, object>> CsvData { get; set; } = new();
        public List<NetworkTrafficModel> ChartData { get; set; } = new();
        public ApexChartOptions<NetworkTrafficModel> ChartOptions { get; set; } = new();
        public string DataDate { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
    }
    
    private Timer? _scrapeTimer;
    private Timer? _countdownTimer;

    protected override async Task OnInitializedAsync()
    {
        countdownSeconds = scrapeIntervalMinutes * 60;
        // 頁面載入時自動爬取所有時間範圍的資料
        await ScrapeAllTimeRanges();
        
        // 設定爬蟲計時器
        _scrapeTimer = new Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                await ScrapeAllTimeRanges();
                countdownSeconds = scrapeIntervalMinutes * 60; // 重設倒數計時
                StateHasChanged();
            });
        }, null, TimeSpan.FromMinutes(scrapeIntervalMinutes), TimeSpan.FromMinutes(scrapeIntervalMinutes));

        // 設定每秒更新倒數計時的計時器
        _countdownTimer = new Timer(_ =>
        {
            InvokeAsync(() =>
            {
                if (countdownSeconds > 0)
                {
                    countdownSeconds--;
                }
                StateHasChanged();
            });
        }, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    public void Dispose()
    {
        _scrapeTimer?.Dispose();
        _countdownTimer?.Dispose();
    }
    
    private void GenerateTestData()
    {
        try
        {
            allTimeRangeData.Clear();
            
            foreach (var timeRange in timeRanges)
            {
                var testData = new TimeRangeData
                {
                    ChartData = GenerateTestChartData(timeRange),
                    CsvData = new List<Dictionary<string, object>>(),
                    CsvContent = "",
                    DataDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    ErrorMessage = ""
                };
                
                // 設定獨立的圖表選項
                testData.ChartOptions = SetupChartOptions(timeRange);
                
                // 轉換圖表資料為表格資料
                testData.CsvData = testData.ChartData.Select(d => new Dictionary<string, object>
                {
                    ["時間"] = d.Time,
                    ["入站流量"] = d.Inbound,
                    ["出站流量"] = d.Outbound
                }).ToList();
                
                allTimeRangeData[timeRange] = testData;
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"生成測試資料失敗: {ex.Message}";
        }
    }
    
    private List<NetworkTrafficModel> GenerateTestChartData(string timeRange)
    {
        var random = new Random(timeRange.GetHashCode()); // 使用timeRange作為種子，確保每次產生相同的資料
        var data = new List<NetworkTrafficModel>();
        var baseTime = DateTime.Now.AddHours(-24);
        
        // 根據時間範圍產生不同間隔的資料
        var (interval, count, baseInbound, baseOutbound) = timeRange switch
        {
            "Hourly" => (TimeSpan.FromMinutes(5), 12, 100000000, 80000000),    // 5分鐘間隔，12筆資料
            "Daily" => (TimeSpan.FromHours(1), 24, 150000000, 120000000),     // 1小時間隔，24筆資料
            "Weekly" => (TimeSpan.FromHours(6), 28, 200000000, 160000000),    // 6小時間隔，28筆資料
            "Monthly" => (TimeSpan.FromDays(1), 30, 250000000, 200000000),    // 1天間隔，30筆資料
            "Yearly" => (TimeSpan.FromDays(15), 24, 300000000, 240000000),    // 15天間隔，24筆資料
            _ => (TimeSpan.FromHours(1), 10, 100000000, 80000000)
        };
        
        for (int i = 0; i < count; i++)
        {
            var time = baseTime.Add(interval * i);
            
            // 產生有變化的流量數據
            var inboundVariation = random.Next(-30, 50) / 100.0; // -30% to +50%
            var outboundVariation = random.Next(-20, 40) / 100.0; // -20% to +40%
            
            var inbound = (int)(baseInbound * (1 + inboundVariation));
            var outbound = (int)(baseOutbound * (1 + outboundVariation));
            
            data.Add(new NetworkTrafficModel
            {
                Time = time.ToString("yyyy MM dd HH mm"),
                Inbound = Math.Max(0, inbound),
                Outbound = Math.Max(0, outbound),
                OriginalTimeFormat = time.ToString("yyyy/M/d HH:mm")
            });
        }
        
        return data;
    }
    
    private async Task ScrapeAllTimeRanges()
    {
        try
        {
            isLoading = true;
            errorMessage = "";
            successMessage = "";
            allTimeRangeData.Clear();
            StateHasChanged();

            if (string.IsNullOrWhiteSpace(cactiUrl))
            {
                errorMessage = "請輸入 Cacti 圖表 URL";
                return;
            }

            int successCount = 0;
            int totalCount = timeRanges.Count;

            foreach (var timeRange in timeRanges)
            {
                try
                {
                    currentTimeRange = timeRange;
                    StateHasChanged();

                    // 爬取這個時間範圍的資料
                    var csvContent = await WebScrapingService.ScrapeAndDownloadCsvAsync(cactiUrl, timeRange);
                    
                    var timeRangeData = new TimeRangeData();
                    
                    if (string.IsNullOrWhiteSpace(csvContent))
                    {
                        timeRangeData.ErrorMessage = "下載的 CSV 內容為空";
                    }
                    else
                    {
                        // 解析 CSV 資料
                        timeRangeData.CsvContent = csvContent;
                        timeRangeData.CsvData = WebScrapingService.ParseCsvContent(csvContent);
                        
                        if (timeRangeData.CsvData.Any())
                        {
                            // 取得資料日期
                            timeRangeData.DataDate = WebScrapingService.GetFirstDataDate(csvContent);
                            
                            // 轉換為圖表資料，並根據時間範圍進行聚合
                            timeRangeData.ChartData = WebScrapingService.ConvertToChartData(csvContent, timeRange);
                            
                            // 設定圖表選項
                            timeRangeData.ChartOptions = SetupChartOptions(timeRange);
                            
                            successCount++;
                        }
                        else
                        {
                            timeRangeData.ErrorMessage = "CSV 資料解析失敗或無資料";
                        }
                    }
                    
                    allTimeRangeData[timeRange] = timeRangeData;
                }
                catch (Exception ex)
                {
                    allTimeRangeData[timeRange] = new TimeRangeData
                    {
                        ErrorMessage = $"處理失敗: {ex.Message}"
                    };
                }
            }

            currentTimeRange = "";
            successMessage = $"完成資料爬取！成功處理 {successCount}/{totalCount} 個時間範圍";
            
            if (successCount == 0)
            {
                errorMessage = "所有時間範圍的資料都無法成功載入";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"批次爬取失敗: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            currentTimeRange = "";
            StateHasChanged();
        }
    }

    private async Task DownloadCsvFile(string timeRange)
    {
        try
        {
            if (!allTimeRangeData.ContainsKey(timeRange) || 
                string.IsNullOrEmpty(allTimeRangeData[timeRange].CsvContent))
            {
                errorMessage = $"沒有可下載的 {timeRange} CSV 資料";
                return;
            }

            var csvContent = allTimeRangeData[timeRange].CsvContent;
            var fileName = $"yuntech_traffic_{timeRange}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var bytes = System.Text.Encoding.UTF8.GetBytes(csvContent);
            var base64 = Convert.ToBase64String(bytes);

            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64, "text/csv");

            successMessage = $"CSV 檔案 {fileName} 已下載";
            errorMessage = "";
        }
        catch (Exception ex)
        {
            errorMessage = $"下載檔案失敗: {ex.Message}";
        }
    }

    private ApexChartOptions<NetworkTrafficModel> SetupChartOptions(string timeRange)
    {
        // 為每個時間範圍創建完全獨立的圖表選項物件
        // 使用不同的顏色種子來確保物件的獨立性
        var colorSeed = timeRange switch
        {
            "Hourly" => 0,
            "Daily" => 1,
            "Weekly" => 2,
            "Monthly" => 3,
            "Yearly" => 4,
            _ => 0
        };
        
        var colors = ChartDataService.GetStandardColors(2, colorSeed);
        
        // 為每個時間範圍創建獨立的圖表選項
        var chartOptions = ChartDataService.CreateAreaChartOptions<NetworkTrafficModel>(
            colors: colors,
            showDataLabels: false,
            isSpline: true,
            isStacked: false,
            isMarkers: true
        );

        // 針對網路流量圖表的特殊設定 - 每次都創建新的物件
        chartOptions.Xaxis = new XAxis();
        chartOptions.Xaxis.Title = new AxisTitle {
            Text = "時間",
            Style = new AxisTitleStyle { FontSize = "14px", Color = "#fff" }
        };
        
        // 根據時間範圍設定不同的標籤顯示邏輯
        chartOptions.Xaxis.Labels = new XAxisLabels
        {
            Style = new AxisLabelStyle { FontSize = "12px", Colors = new List<string> { "#fff" } },
            ShowDuplicates = false,
            Rotate = 0,
            RotateAlways = false
        };

        switch (timeRange)
        {
            case "Hourly":
                // 每20分鐘顯示標籤，只顯示 HH:mm 格式
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';

                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);
                    if (minute % 20 === 0) {
                        return hour + ':' + (minute < 10 ? '0' + minute : minute);
                    }
                    return '';
                }";
                break;

            case "Daily":
                // 每2小時顯示標籤
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';

                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);
                    if (hour % 2 === 0 && minute === 0) {
                        return hour + ':' + (minute < 10 ? '0' + minute : minute);
                    }

                    return '';
                }";
                break;
            case "Weekly":
                // 每日顯示標籤
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';

                    var mon = parseInt(parts[1]);
                    var day = parseInt(parts[2]);
                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);

                    if (hour % 12 === 0 && minute === 0) {
                        return mon + '/' + day;
                    }
                    return '';
                }";
                break;

            case "Monthly":
                // 每周顯示標籤，顯示該周為整年的第幾周
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';
                    
                    // 解析日期：year, month, day
                    var year = parseInt(parts[0]);
                    var month = parseInt(parts[1]);
                    var day = parseInt(parts[2]);
                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);

                    // 創建 Date 物件
                    var date = new Date(year, month - 1, day);
                    
                    // 計算該日期是一年中的第幾周
                    var startOfYear = new Date(year, 0, 1);
                    var diff = date - startOfYear;
                    var weekNumber = Math.ceil((diff / (24 * 60 * 60 * 1000) + startOfYear.getDay() + 1) / 7);

                    if (day % 7 == 0 && hour > 20) {
                        return 'week' + weekNumber;
                    }
                    return '';
                }";
                break;

            case "Yearly":
                // 每月顯示標籤
                chartOptions.Xaxis.Labels.Formatter = @"function(value, timestamp, index, opts) {
                    if (!value) return '';
                    var parts = value.split(' ');
                    if (parts.length !== 5) return '';
                    
                    // 解析月份
                    var year = parseInt(parts[0]);
                    var month = parseInt(parts[1]);
                    var day = parseInt(parts[2]);
                    var hour = parseInt(parts[3]);
                    var minute = parseInt(parts[4]);
                    
                    var monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                                    '7月', '8月', '9月', '10月', '11月', '12月'];
                    var label = monthNames[month - 1] || monthNames[0];

                    // 閏年                    
                    if(year % 4 ==0 && year % 100 != 0){
                        if(month == 2 && day % 29 ==0)
                        {
                            return label;
                        }
                        else if (day % 30 == 0){
                            return label;
                        }
                    }
                    else{
                    if(month == 2 && day % 28 ==0)
                        {
                            return label;
                        }
                        else if (day % 30 == 0){
                            return label;
                        }
                    }

                    return '';
                }";
                break;

            default:
                // 預設情況，不添加特殊格式化
                break;
        }
        
        // 確保 Yaxis 是新的陣列
        chartOptions.Yaxis = new List<YAxis> { new YAxis() };
        chartOptions.Yaxis[0].Title = new AxisTitle {
            Text = "流量 (bps)",
            Style = new AxisTitleStyle { FontSize = "14px", Color = "#fff" }
        };
        
        // 設定圖例 - 創建新的 Legend 物件
        chartOptions.Legend = new Legend
        {
            Position = LegendPosition.Top,
            FontSize = "14px"
        };
        
        return chartOptions;
    }
}